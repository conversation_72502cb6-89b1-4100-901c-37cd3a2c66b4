import requests
import json

# 基础URL
BASE_URL = "http://localhost:8080/api/v1"

# 测试知识库存储
def test_store_knowledge():
    print("测试 storeKnowledge API...")
    knowledge_data = {
        "content": "这是一个关于Python编程的知识文档。Python是一种面向对象的解释型编程语言，由吉多·范罗苏姆创建于1989年底。Python是纯粹的自由软件，源代码和解释器CPython遵循GPL（GNU General Public License）协议。Python语法简洁而清晰，具有丰富而强大的类库，常被称为胶水语言，可以把用其他语言制作的各种模块很轻松地联结在一起。",
        "metadata": {
            "title": "Python基础知识",
            "author": "萤火AI百宝箱",
            "category": "编程",
            "tags": ["Python", "编程语言", "基础知识"]
        }
    }
    
    response = requests.post(f"{BASE_URL}/storeKnowledge", json=knowledge_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 201:
        print("知识存储成功!")
    else:
        print(f"错误: {response.text}")
    print("-" * 50)

# 测试知识库搜索
def test_search_knowledge():
    print("测试 searchKnowledge API...")
    search_data = {
        "query": "Python编程语言有哪些特点?",
        "size": 5
    }
    
    response = requests.post(f"{BASE_URL}/searchKnowledge", json=search_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        results = response.json()
        print(f"找到 {len(results)} 条匹配结果:")
        for i, result in enumerate(results, 1):
            print(f"结果 {i}:")
            print(f"内容: {result['content'][:100]}...")
            print(f"元数据: {json.dumps(result['metadata'], ensure_ascii=False)}")
            print()
    else:
        print(f"错误: {response.text}")
    print("-" * 50)

# 测试FAQ存储
def test_store_faq():
    print("测试 storeFAQ API...")
    faq_data = {
        "question": "如何在Python中创建一个虚拟环境?",
        "answer": "在Python中创建虚拟环境可以使用venv模块。首先，打开命令行终端，然后执行以下命令：\n\n```bash\npython -m venv myenv\n```\n\n其中myenv是虚拟环境的名称。创建完成后，使用以下命令激活虚拟环境：\n\n- Windows: `myenv\\Scripts\\activate`\n- macOS/Linux: `source myenv/bin/activate`\n\n激活后，终端提示符前会显示虚拟环境名称，表示当前处于虚拟环境中。"
    }
    
    response = requests.post(f"{BASE_URL}/storeFAQ", json=faq_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 201:
        print("FAQ存储成功!")
    else:
        print(f"错误: {response.text}")
    print("-" * 50)

# 测试FAQ搜索
def test_search_faq():
    print("测试 searchFAQ API...")
    search_data = {
        "query": "怎么搭建Python隔离环境?",
        "size": 3
    }
    
    response = requests.post(f"{BASE_URL}/searchFAQ", json=search_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        results = response.json()
        print(f"找到 {len(results)} 条匹配结果:")
        for i, result in enumerate(results, 1):
            print(f"结果 {i}:")
            print(f"问题: {result['question']}")
            print(f"答案: {result['answer'][:100]}...")
            print()
    else:
        print(f"错误: {response.text}")
    print("-" * 50)

if __name__ == "__main__":
    # 按顺序执行测试
    test_store_knowledge()
    test_search_knowledge()
    test_store_faq()
    test_search_faq()
