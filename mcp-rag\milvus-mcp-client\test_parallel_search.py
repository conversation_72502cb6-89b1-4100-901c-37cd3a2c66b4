#!/usr/bin/env python3
"""
并行检索测试脚本
用于验证并行检索功能的性能和正确性
"""
import asyncio
import time
from typing import List
from loguru import logger

from app.knowledge_retriever import KnowledgeRetriever

async def test_parallel_search():
    """测试并行检索功能"""
    
    # 初始化检索器
    retriever = KnowledgeRetriever(max_search_results=3)
    
    # 测试问题
    test_question = "RAG技术在企业知识管理中的应用优势和实施挑战是什么？"
    
    logger.info("=" * 80)
    logger.info("开始测试并行检索功能")
    logger.info(f"测试问题: {test_question}")
    logger.info("=" * 80)
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 执行查询
        answer = await retriever.query(test_question)
        
        # 记录结束时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 输出结果
        logger.info("=" * 80)
        logger.info("测试结果")
        logger.info("=" * 80)
        logger.info(f"执行时间: {execution_time:.2f} 秒")
        logger.info(f"回答长度: {len(answer)} 字符")
        logger.info("=" * 80)
        logger.info("生成的回答:")
        logger.info("-" * 80)
        print(answer)
        logger.info("=" * 80)
        
        # 关闭连接
        await retriever.mcp_client.close()
        
        logger.info("测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        # 确保连接被关闭
        try:
            await retriever.mcp_client.close()
        except:
            pass

async def test_question_decomposition():
    """测试问题分解功能"""
    
    retriever = KnowledgeRetriever()
    
    test_questions = [
        "什么是RAG？",
        "RAG技术的优势和挑战",
        "如何在企业中实施RAG系统，需要考虑哪些技术架构和数据安全问题？",
        "比较传统搜索引擎和基于RAG的智能问答系统在准确性、实时性和用户体验方面的差异"
    ]
    
    logger.info("=" * 80)
    logger.info("测试问题分解功能")
    logger.info("=" * 80)
    
    for i, question in enumerate(test_questions, 1):
        logger.info(f"\n测试问题 {i}: {question}")
        logger.info("-" * 60)
        
        try:
            # 测试问题分解
            sub_questions = await retriever._decompose_question(question)
            
            logger.info(f"分解为 {len(sub_questions)} 个子问题:")
            for j, sub_q in enumerate(sub_questions, 1):
                logger.info(f"  {j}. {sub_q}")
                
        except Exception as e:
            logger.error(f"分解问题时发生错误: {e}")
    
    logger.info("\n" + "=" * 80)
    logger.info("问题分解测试完成！")

if __name__ == "__main__":
    # 设置日志级别
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )
    
    print("选择测试模式:")
    print("1. 完整并行检索测试")
    print("2. 问题分解测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_parallel_search())
    elif choice == "2":
        asyncio.run(test_question_decomposition())
    else:
        print("无效选择，退出程序")
