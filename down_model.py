from modelscope import snapshot_download
model_dir = snapshot_download('maidalun/bce-embedding-base_v1', 
                              cache_dir='demo/', 
                              )

# import os
# os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"  # 设置为hf的国内镜像网站

# from huggingface_hub import snapshot_download

# model_name = "maidalun1020/bce-embedding-base_v1"
# # while True 是为了防止断联
# while True:
#     try:
#         snapshot_download(
#             repo_id=model_name,
#             local_dir_use_symlinks=True,  # 在local-dir指定的目录中都是一些“链接文件”
#            # ignore_patterns=["*.bin"],  # 忽略下载哪些文件
#             local_dir=model_name,
#             # token="*************",   # huggingface的token
#             resume_download=True
#         )
#         break
#     except:
#         pass